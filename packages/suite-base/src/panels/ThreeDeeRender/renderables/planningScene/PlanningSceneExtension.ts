// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

import * as _ from "lodash-es";
import * as THREE from "three";

import { filterMap } from "@lichtblick/den/collection";
import Logger from "@lichtblick/log";
import { toNanoSec } from "@lichtblick/rostime";
import { SettingsTreeAction, SettingsTreeFields, SettingsTreeChildren } from "@lichtblick/suite";

import { CollisionObjectRenderable, CollisionObjectUserData, CollisionObjectSettings } from "./CollisionObjectRenderable";
import {
  PlanningScene,
  GetPlanningSceneRequest,
  GetPlanningSceneResponse,
  createDefaultPlanningSceneComponents,
  DEFAULT_PLANNING_SCENE_SERVICE,
  DEFAULT_PLANNING_SCENE_TOPIC,
  PLANNING_SCENE_DATATYPES,
  CollisionObject,
  CollisionObjectOperation,
  Mesh,
  SolidPrimitive,
  SolidPrimitiveType,
  RobotState,
  AttachedCollisionObject,
} from "./types";
import type { IRenderer, AnyRendererSubscription } from "../../IRenderer";
import { Renderable } from "../../Renderable";
import { SceneExtension, PartialMessageEvent, PartialMessage } from "../../SceneExtension";
import { SettingsTreeEntry, SettingsTreeNodeWithActionHandler } from "../../SettingsManager";
import { stringToRgba } from "../../color";
import { Header } from "../../ros";
import { BaseSettings, CustomLayerSettings } from "../../settings";
import { AnyFrameId, Pose } from "../../transforms";

// Error constants for settings tree
const SERVICE_ERROR = "SERVICE_ERROR";
const MESSAGE_PROCESSING_ERROR = "MESSAGE_PROCESSING_ERROR";

// Layer ID for custom layers
const LAYER_ID = "foxglove.PlanningScene";

// Default settings for topic-based planning scenes
export type PlanningSceneSettings = BaseSettings & {
  defaultColor: string;
  sceneOpacity: number;
  showCollisionObjects: boolean;
  showAttachedObjects: boolean;
  showOctomap: boolean;
};

// Settings for custom planning scene layers
export type LayerSettingsPlanningScene = CustomLayerSettings & {
  layerId: "foxglove.PlanningScene";
  topic: string;
  defaultColor: string;
  sceneOpacity: number;
  showCollisionObjects: boolean;
  showAttachedObjects: boolean;
  showOctomap: boolean;
  // Store per-collision-object settings
  collisionObjects?: Record<string, Partial<CollisionObjectSettings>>;
};

const DEFAULT_PLANNING_SCENE_SETTINGS: PlanningSceneSettings = {
  visible: true,
  defaultColor: "#ffffff",
  sceneOpacity: 1.0,
  showCollisionObjects: true,
  showAttachedObjects: true,
  showOctomap: true,
};

const DEFAULT_CUSTOM_SETTINGS: LayerSettingsPlanningScene = {
  visible: true,
  frameLocked: true,
  label: "Planning Scene",
  instanceId: "invalid",
  layerId: LAYER_ID,
  topic: DEFAULT_PLANNING_SCENE_TOPIC,
  defaultColor: "#ffffff",
  sceneOpacity: 1.0,
  showCollisionObjects: true,
  showAttachedObjects: true,
  showOctomap: true,
};

const log = Logger.getLogger(__filename);

export class PlanningSceneExtension extends SceneExtension<CollisionObjectRenderable> {
  public static extensionId = "foxglove.PlanningScene";

  private currentScene?: PartialMessage<PlanningScene>;
  private serviceClient?: (service: string, request: unknown) => Promise<unknown>;
  private initialSceneFetched = false;
  private fetchingInitialScene = false;
  private pendingServiceCall?: Promise<unknown>; // Track pending service calls for cleanup

  // Track current message context for creating renderables
  private topic = DEFAULT_PLANNING_SCENE_TOPIC;
  private receiveTime = 0n;
  private messageTime = 0n;
  private currentInstanceId?: string; // Track which layer instance is currently being processed

  // Settings
  private settings: PlanningSceneSettings = { ...DEFAULT_PLANNING_SCENE_SETTINGS };

  // Performance optimization: Track object hashes for differential updates
  private objectHashes = new Map<string, string>();

  // Performance optimization: Geometry sharing cache
  private geometryCache = new Map<string, THREE.BufferGeometry>();

  // Performance optimization: Material sharing cache for identical colors/transparency
  private materialCache = new Map<string, THREE.MeshStandardMaterial>();

  // Performance optimization: Lazy loading cache for mesh resources
  private meshResourceCache = new Map<string, Promise<THREE.BufferGeometry>>();

  // Performance monitoring: Track visible object count for optimization insights
  private visibleObjectCount = 0;

  // Performance optimization: Reusable objects for frustum culling
  private frustum = new THREE.Frustum();
  private cameraMatrix = new THREE.Matrix4();

  // Helper method to find the instance ID for a given topic
  private findInstanceIdForTopic(topic: string): string | undefined {

    // First try to find a layer with matching topic
    for (const [instanceId, layerConfig] of Object.entries(this.renderer.config.layers)) {
      if (layerConfig?.layerId === LAYER_ID) {
        const config = layerConfig as Partial<LayerSettingsPlanningScene>;
        if (config.topic === topic) {
          return instanceId;
        }
      }
    }

    // If no exact match, use the first available planning scene layer
    // (planning scene layers can listen to any planning scene topic)
    for (const [instanceId, layerConfig] of Object.entries(this.renderer.config.layers)) {
      if (layerConfig?.layerId === LAYER_ID) {
        return instanceId;
      }
    }

    return undefined;
  }

  // Helper method to load settings from layer configuration
  private loadSettingsFromConfig(instanceId?: string): PlanningSceneSettings {
    if (!instanceId || !this.renderer.config.layers[instanceId]) {
      return DEFAULT_PLANNING_SCENE_SETTINGS;
    }

    const layerConfig = this.renderer.config.layers[instanceId] as Partial<LayerSettingsPlanningScene>;

    return {
      visible: layerConfig.visible ?? DEFAULT_PLANNING_SCENE_SETTINGS.visible,
      defaultColor: layerConfig.defaultColor ?? DEFAULT_PLANNING_SCENE_SETTINGS.defaultColor,
      sceneOpacity: layerConfig.sceneOpacity ?? DEFAULT_PLANNING_SCENE_SETTINGS.sceneOpacity,
      showCollisionObjects: layerConfig.showCollisionObjects ?? DEFAULT_PLANNING_SCENE_SETTINGS.showCollisionObjects,
      showAttachedObjects: layerConfig.showAttachedObjects ?? DEFAULT_PLANNING_SCENE_SETTINGS.showAttachedObjects,
      showOctomap: layerConfig.showOctomap ?? DEFAULT_PLANNING_SCENE_SETTINGS.showOctomap,
    };
  }

  // Helper method to extract color for a specific collision object from planning scene
  private getObjectColorFromScene(objectId: string): { color: string; opacity: number } | undefined {
    if (!this.currentScene?.object_colors) {
      log.warn(`No object colors found for object ${objectId}`);
      return undefined;
    }

    // Find the color for this specific object ID
    const objectColor = this.currentScene.object_colors.find(oc => oc?.id === objectId);
    if (!objectColor?.color) {
      return undefined;
    }

    const { r, g, b, a } = objectColor.color;

    // Ensure all color components are defined and valid
    if (r == undefined || g == undefined || b == undefined || a == undefined) {
      log.warn(`Invalid color values for object ${objectId}: ${r}, ${g}, ${b}, ${a}`);
      return undefined;
    }

    // Convert RGBA values (0-1) to hex color string
    const rHex = Math.round(r * 255).toString(16).padStart(2, '0');
    const gHex = Math.round(g * 255).toString(16).padStart(2, '0');
    const bHex = Math.round(b * 255).toString(16).padStart(2, '0');
    const color = `#${rHex}${gHex}${bHex}`;

    return {
      color,
      opacity: a, // Alpha value is already in 0-1 range
    };
  }

  public constructor(
    renderer: IRenderer,
    serviceClient?: (service: string, request: unknown) => Promise<unknown>,
    name: string = PlanningSceneExtension.extensionId
  ) {
    super(name, renderer);
    this.serviceClient = serviceClient;

    // Register custom layer action (if available)
    renderer.addCustomLayerAction({
      layerId: LAYER_ID,
      label: "Add Planning Scene",
      icon: "PrecisionManufacturing",
      handler: this.#handleAddPlanningScene,
    });

    // Load existing planning scene layers from the config (if available)
    const planningSceneLayers = Object.entries(renderer.config.layers).filter(([, entry]) => entry?.layerId === LAYER_ID);

    for (const [instanceId, entry] of planningSceneLayers) {
      this.#updatePlanningSceneLayer(instanceId, entry as Partial<LayerSettingsPlanningScene>);
    }
  }

  // Compute hash for collision object to detect changes for performance optimization
  private computeObjectHash(object: CollisionObject): string {
    // Create a hash based on object properties that affect rendering
    // Use a more efficient hash computation to avoid JSON.stringify overhead
    const parts: string[] = [
      object.id,
      String(object.operation),
      object.header.frame_id,
    ];

    // Round pose values to avoid floating point precision issues
    const roundedPose = {
      x: Math.round(object.pose.position.x * 1000) / 1000,
      y: Math.round(object.pose.position.y * 1000) / 1000,
      z: Math.round(object.pose.position.z * 1000) / 1000,
      qx: Math.round(object.pose.orientation.x * 1000) / 1000,
      qy: Math.round(object.pose.orientation.y * 1000) / 1000,
      qz: Math.round(object.pose.orientation.z * 1000) / 1000,
      qw: Math.round(object.pose.orientation.w * 1000) / 1000,
    };
    parts.push(`${roundedPose.x},${roundedPose.y},${roundedPose.z},${roundedPose.qx},${roundedPose.qy},${roundedPose.qz},${roundedPose.qw}`);

    // Hash primitives with rounded dimensions
    for (let i = 0; i < object.primitives.length; i++) {
      const p = object.primitives[i];
      const pose = object.primitive_poses[i];
      if (p && pose) {
        const roundedDims = p.dimensions.map(d => Math.round(d * 1000) / 1000);
        const roundedPrimPose = {
          x: Math.round(pose.position.x * 1000) / 1000,
          y: Math.round(pose.position.y * 1000) / 1000,
          z: Math.round(pose.position.z * 1000) / 1000,
        };
        parts.push(`p${i}:${p.type}:${roundedDims.join(',')}:${roundedPrimPose.x},${roundedPrimPose.y},${roundedPrimPose.z}`);
      }
    }

    // Hash meshes (use vertex/triangle counts and first few vertices for performance)
    for (let i = 0; i < object.meshes.length; i++) {
      const m = object.meshes[i];
      const pose = object.mesh_poses[i];
      if (m && pose) {
        // Create a simple structural hash of the mesh
        let structureHash = m.vertices.length * 1000 + m.triangles.length;
        // Sample first few vertices for uniqueness
        for (let j = 0; j < Math.min(m.vertices.length, 3); j++) {
          const v = m.vertices[j];
          if (v) {
            structureHash += Math.round(v.x * 100) + Math.round(v.y * 10) + Math.round(v.z);
          }
        }
        const roundedMeshPose = {
          x: Math.round(pose.position.x * 1000) / 1000,
          y: Math.round(pose.position.y * 1000) / 1000,
          z: Math.round(pose.position.z * 1000) / 1000,
        };
        parts.push(`m${i}:${structureHash}:${roundedMeshPose.x},${roundedMeshPose.y},${roundedMeshPose.z}`);
      }
    }

    // Hash planes
    for (let i = 0; i < object.planes.length; i++) {
      const pose = object.plane_poses[i];
      if (pose) {
        const roundedPlanePose = {
          x: Math.round(pose.position.x * 1000) / 1000,
          y: Math.round(pose.position.y * 1000) / 1000,
          z: Math.round(pose.position.z * 1000) / 1000,
        };
        parts.push(`pl${i}:${roundedPlanePose.x},${roundedPlanePose.y},${roundedPlanePose.z}`);
      }
    }

    return parts.join('|');
  }

  // Check if object has changed since last update for performance optimization
  private hasObjectChanged(object: CollisionObject): boolean {
    const currentHash = this.computeObjectHash(object);
    const previousHash = this.objectHashes.get(object.id);

    if (previousHash !== currentHash) {
      this.objectHashes.set(object.id, currentHash);
      return true;
    }

    return false;
  }

  // Generate geometry cache key for sharing identical shapes to improve performance
  private generateGeometryKey(type: string, dimensions: number[]): string {
    // Round dimensions to avoid floating point precision issues
    const roundedDims = dimensions.map(d => Math.round(d * 1000) / 1000);
    return `${type}_${roundedDims.join('_')}`;
  }

  // Get or create shared geometry to improve performance by reusing identical shapes
  public getSharedGeometry(type: string, dimensions: number[], createGeometry: () => THREE.BufferGeometry): THREE.BufferGeometry {
    const key = this.generateGeometryKey(type, dimensions);

    let geometry = this.geometryCache.get(key);
    if (!geometry) {
      geometry = createGeometry();
      this.geometryCache.set(key, geometry);
    }

    return geometry;
  }

  // Performance optimization: Get or create shared material for identical colors/transparency
  public getSharedMaterial(color: string, opacity: number): THREE.MeshStandardMaterial {
    // Create a cache key based on color and opacity
    const key = `${color}_${Math.round(opacity * 1000)}`;

    let material = this.materialCache.get(key);
    if (!material) {
      // Parse color string to RGBA
      const tempColor = { r: 0, g: 0, b: 0, a: opacity };
      stringToRgba(tempColor, color);
      tempColor.a = opacity;

      // Create new material
      material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(tempColor.r, tempColor.g, tempColor.b).convertSRGBToLinear(),
        metalness: 0,
        roughness: 1,
        dithering: true,
        opacity: tempColor.a,
        transparent: tempColor.a < 1,
        depthWrite: tempColor.a === 1,
      });

      this.materialCache.set(key, material);
    }

    return material;
  }

  // Get shared geometry for mesh data to improve performance by reusing identical meshes
  public getSharedMeshGeometry(meshData: Mesh): string {
    // Create a cache key based on mesh structure for sharing identical meshes
    const vertexCount = meshData.vertices.length;
    const triangleCount = meshData.triangles.length;

    // Create a simple hash of the mesh structure
    let structureHash = 0;
    for (let i = 0; i < Math.min(vertexCount, 10); i++) {
      const v = meshData.vertices[i];
      if (v) {
        structureHash += v.x * 1000 + v.y * 100 + v.z * 10;
      }
    }

    return `mesh_${vertexCount}_${triangleCount}_${Math.round(structureHash)}`;
  }

  // Lazy loading for mesh resources to improve performance by deferring expensive operations
  public async loadMeshResource(meshData: Mesh): Promise<THREE.BufferGeometry> {
    // Use the shared mesh geometry key for better cache efficiency
    const meshKey = this.getSharedMeshGeometry(meshData);

    let geometryPromise = this.meshResourceCache.get(meshKey);
    if (!geometryPromise) {
      geometryPromise = this.createMeshGeometry(meshData);
      this.meshResourceCache.set(meshKey, geometryPromise);

      // Performance optimization: Clean up failed promises from cache
      geometryPromise.catch(() => {
        this.meshResourceCache.delete(meshKey);
      });
    }

    return await geometryPromise;
  }

  // Helper method to create mesh geometry asynchronously with better error handling
  private async createMeshGeometry(meshData: Mesh): Promise<THREE.BufferGeometry> {
    return await new Promise((resolve, reject) => {
      // Use requestAnimationFrame to avoid blocking the main thread for large meshes
      requestAnimationFrame(() => {
        try {
          const geometry = new THREE.BufferGeometry();

          // Validate mesh data
          if (meshData.vertices.length === 0) {
            throw new Error("Mesh has no vertices");
          }
          if (meshData.triangles.length === 0) {
            throw new Error("Mesh has no triangles");
          }

          // Convert mesh data to THREE.js format
          const vertices: number[] = [];
          const indices: number[] = [];

          // Add vertices with validation
          for (const vertex of meshData.vertices) {
            if (!Number.isFinite(vertex.x) || !Number.isFinite(vertex.y) || !Number.isFinite(vertex.z)) {
              throw new Error("Invalid vertex data");
            }
            vertices.push(vertex.x, vertex.y, vertex.z);
          }

          // Add triangle indices with validation
          for (let j = 0; j < meshData.triangles.length; j++) {
            const triangle = meshData.triangles[j];
            if (triangle?.vertex_indices == undefined) {
              throw new Error(`Triangle at index ${j} has no vertex_indices property`);
            }

            // Accept both regular arrays and typed arrays (like Uint32Array from ROS)
            const vertexIndices = triangle.vertex_indices;
            const isArrayLike = Array.isArray(vertexIndices) ||
              (typeof vertexIndices === 'object' &&
                'length' in vertexIndices &&
                typeof (vertexIndices as ArrayLike<unknown>).length === 'number');

            if (!isArrayLike) {
              throw new Error(`Triangle at index ${j} has vertex_indices that is not an array or array-like: ${typeof triangle.vertex_indices}`);
            }
            if ((vertexIndices as ArrayLike<unknown>).length !== 3) {
              throw new Error(`Triangle at index ${j} has ${(vertexIndices as ArrayLike<unknown>).length} vertex indices, expected 3`);
            }
            for (const index of triangle.vertex_indices) {
              if (!Number.isInteger(index) || index < 0 || index >= meshData.vertices.length) {
                throw new Error(`Triangle at index ${j} has invalid vertex index: ${index} (must be 0-${meshData.vertices.length - 1})`);
              }
            }
            indices.push(...triangle.vertex_indices);
          }

          geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
          geometry.setIndex(indices);
          geometry.computeVertexNormals();
          geometry.computeBoundingSphere();

          resolve(geometry);
        } catch (error) {
          reject(error instanceof Error ? error : new Error(String(error)));
        }
      });
    });
  }

  public override dispose(): void {
    // Clear performance optimization caches
    this.objectHashes.clear();

    // Dispose shared geometries
    for (const geometry of this.geometryCache.values()) {
      geometry.dispose();
    }
    this.geometryCache.clear();

    // Dispose shared materials
    for (const material of this.materialCache.values()) {
      material.dispose();
    }
    this.materialCache.clear();

    // Clear mesh resource cache
    this.meshResourceCache.clear();

    // Clear any pending service calls by resetting the service client
    this.serviceClient = undefined;

    // Clear pending service call reference (the promise will still resolve/reject but we won't handle it)
    this.pendingServiceCall = undefined;

    // Clear current scene data
    this.currentScene = undefined;

    // Reset state flags
    this.initialSceneFetched = false;
    this.fetchingInitialScene = false;

    // Clear all settings errors for this extension
    this.renderer.settings.errors.clearPath(["extensions", PlanningSceneExtension.extensionId]);

    // Clean up subscription and renderables (this calls dispose() on all renderables)
    super.dispose();
  }

  public override settingsNodes(): SettingsTreeEntry[] {
    const entries: SettingsTreeEntry[] = [];

    // Note: Main extension settings are removed - all settings now only appear in Custom Layers
    // This ensures Service Status and Collision Objects sections only show up under Custom Layers

    // Custom layer entries (if config.layers is available)
    for (const [instanceId, layerConfig] of Object.entries(this.renderer.config.layers)) {
      if (layerConfig?.layerId === LAYER_ID) {
        const config = layerConfig as Partial<LayerSettingsPlanningScene>;

        const fields: SettingsTreeFields = {
          topic: {
            label: "Topic",
            input: "autocomplete",
            value: config.topic ?? DEFAULT_CUSTOM_SETTINGS.topic,
            items: filterMap(this.renderer.topics ?? [], (_topic) =>
              PLANNING_SCENE_DATATYPES.has(_topic.schemaName) ? _topic.name : undefined,
            ),
            help: "Planning scene topic to subscribe to",
          },
          defaultColor: {
            label: "Default color",
            input: "rgb",
            value: config.defaultColor ?? DEFAULT_CUSTOM_SETTINGS.defaultColor,
            help: "Default color for collision objects without specified colors",
          },
          sceneOpacity: {
            label: "Scene opacity",
            input: "number",
            min: 0,
            max: 1,
            step: 0.1,
            precision: 1,
            value: config.sceneOpacity ?? DEFAULT_CUSTOM_SETTINGS.sceneOpacity,
            help: "Opacity multiplier applied to all collision objects in the scene",
          },
          showCollisionObjects: {
            label: "Show collision objects",
            input: "boolean",
            value: config.showCollisionObjects ?? DEFAULT_CUSTOM_SETTINGS.showCollisionObjects,
          },
          showAttachedObjects: {
            label: "Show attached objects",
            input: "boolean",
            value: config.showAttachedObjects ?? DEFAULT_CUSTOM_SETTINGS.showAttachedObjects,
          },
          showOctomap: {
            label: "Show octomap",
            input: "boolean",
            value: config.showOctomap ?? DEFAULT_CUSTOM_SETTINGS.showOctomap,
          },
        };

        // Add Service Status and Collision Objects sections to each custom layer
        const layerPath = ["layers", instanceId];
        const serviceError = this.renderer.settings.errors.errors.errorAtPath([...layerPath, "service"]);
        const messageError = this.renderer.settings.errors.errors.errorAtPath([...layerPath, "messageProcessing"]);

        const children: SettingsTreeChildren = {};

        // Add service status information for this layer
        children.service = {
          label: "Service Status",
          fields: {
            serviceName: {
              label: "Service",
              input: "string",
              readonly: true,
              value: DEFAULT_PLANNING_SCENE_SERVICE,
              help: "ROS service used to fetch the initial planning scene",
            },
            topic: {
              label: "Topic",
              input: "string",
              readonly: true,
              value: config.topic ?? DEFAULT_CUSTOM_SETTINGS.topic,
              help: "Planning scene topic to subscribe to",
            },
            status: {
              label: "Initial scene",
              input: "string",
              readonly: true,
              value: this.initialSceneFetched ? "Loaded" : this.fetchingInitialScene ? "Loading..." : "Not loaded",
              help: "Status of the initial planning scene fetch",
            },
          },
          actions: [
            {
              type: "action" as const,
              id: "refetchService",
              label: "Refetch",
            },
          ],
          error: serviceError,
        };

        // Add collision objects section for this layer
        if (this.renderables.size > 0) {
          children.collisionObjects = {
            label: `Collision Objects (${this.renderables.size})`,
            children: this.getCollisionObjectNodes(),
            defaultExpansionState: "collapsed" as const,
          };
        }

        entries.push({
          path: layerPath,
          node: {
            label: config.label ?? "Planning Scene",
            icon: "PrecisionManufacturing",
            fields,
            visible: config.visible ?? DEFAULT_CUSTOM_SETTINGS.visible,
            actions: [
              { type: "action", id: "refetch", label: "Refetch" },
              { type: "action", id: "delete", label: "Delete" },
            ],
            order: layerConfig.order,
            handler: this.#handleLayerSettingsAction,
            children,
            error: messageError,
          },
        });
      }
    }

    return entries;
  }

  private getCollisionObjectNodes(): SettingsTreeChildren {
    const nodes: SettingsTreeChildren = {};

    for (const [objectId, renderable] of this.renderables.entries()) {
      const userData = renderable.userData;
      const settings = userData.settings;
      const collisionObject = userData.collisionObject;

      // Count shapes for display
      const primitiveCount = collisionObject.primitives.length;
      const meshCount = collisionObject.meshes.length;
      const planeCount = collisionObject.planes.length;
      const totalShapes = primitiveCount + meshCount + planeCount;

      const shapeInfo = [];
      if (primitiveCount > 0) { shapeInfo.push(`${primitiveCount} primitive${primitiveCount !== 1 ? 's' : ''}`); }
      if (meshCount > 0) { shapeInfo.push(`${meshCount} mesh${meshCount !== 1 ? 'es' : ''}`); }
      if (planeCount > 0) { shapeInfo.push(`${planeCount} plane${planeCount !== 1 ? 's' : ''}`); }

      const fields: SettingsTreeFields = {
        frameId: {
          label: "Frame ID",
          input: "string",
          readonly: true,
          value: userData.frameId,
        },
        shapeCount: {
          label: "Shapes",
          input: "string",
          readonly: true,
          value: totalShapes > 0 ? shapeInfo.join(", ") : "No shapes",
        },
      };

      // Add shape type visibility controls if there are multiple types
      const hasMultipleTypes = [primitiveCount, meshCount, planeCount].filter(count => count > 0).length > 1;
      if (hasMultipleTypes) {
        if (primitiveCount > 0) {
          fields.showPrimitives = {
            label: "Show primitives",
            input: "boolean",
            value: settings.showPrimitives,
          };
        }
        if (meshCount > 0) {
          fields.showMeshes = {
            label: "Show meshes",
            input: "boolean",
            value: settings.showMeshes,
          };
        }
        if (planeCount > 0) {
          fields.showPlanes = {
            label: "Show planes",
            input: "boolean",
            value: settings.showPlanes,
          };
        }
      }

      // Get transform error for this object
      const transformError = this.renderer.settings.errors.errors.errorAtPath(userData.settingsPath);

      // Get shape creation errors for this object
      const shapeErrors = this.renderer.settings.errors.errors.errorAtPath([...userData.settingsPath, "shapes"]);

      // Combine errors if both exist
      let combinedError = transformError;
      if (transformError && shapeErrors) {
        combinedError = `${transformError}; Shape errors: ${shapeErrors}`;
      } else if (shapeErrors) {
        combinedError = shapeErrors;
      }

      const node: SettingsTreeNodeWithActionHandler = {
        label: objectId,
        fields,
        visible: settings.visible,
        error: combinedError,
        handler: this.#handleLayerSettingsAction,
        defaultExpansionState: "collapsed",
      };

      nodes[objectId] = node;
    }

    return nodes;
  }

  // Note: Main settings action handler removed since all settings are now in Custom Layers

  // Note: handleExtensionSettingsUpdate method removed since all settings are now in Custom Layers

  private handleCollisionObjectSettingsUpdate(objectId: string, input: string, value: unknown): void {
    const renderable = this.renderables.get(objectId);
    if (!renderable) {
      return;
    }

    const settings = renderable.userData.settings;

    // Update the renderable settings
    switch (input) {
      case "visible":

        settings.visible = value as boolean;
        // Recalculate visibility based on all layers (will be done in next frame)
        // Don't set renderable.visible directly here, let startFrame() handle it
        break;
      case "showPrimitives":
        settings.showPrimitives = value as boolean;
        // Trigger visual update
        renderable.update(renderable.userData.collisionObject);
        break;
      case "showMeshes":
        settings.showMeshes = value as boolean;
        // Trigger visual update
        renderable.update(renderable.userData.collisionObject);
        break;
      case "showPlanes":
        settings.showPlanes = value as boolean;
        // Trigger visual update
        renderable.update(renderable.userData.collisionObject);
        break;
    }

    // Save the setting to the configuration
    this.saveCollisionObjectSetting(objectId, input, value);
  }

  // Helper method to save collision object settings to the configuration
  private saveCollisionObjectSetting(objectId: string, input: string, value: unknown): void {
    // Find the instance ID for this collision object
    const instanceId = this.findInstanceIdForCollisionObject(objectId);
    if (!instanceId) {

      return; // Can't save without knowing which layer instance this belongs to
    }

    // Save to configuration
    this.renderer.updateConfig((draft) => {
      // Layers structure is always defined in config

      // CRITICAL: Do not create a new layer if it doesn't exist!
      // This was causing layer duplication
      if (!draft.layers[instanceId]) {
        log.error(`Layer ${instanceId} does not exist in config! Cannot save collision object setting.`);
        return; // Exit early to prevent creating duplicate layers
      }

      const layerConfig = draft.layers[instanceId] as LayerSettingsPlanningScene;

      // Ensure collisionObjects structure exists
      if (!layerConfig.collisionObjects) {
        layerConfig.collisionObjects = {};
      }
      if (!layerConfig.collisionObjects[objectId]) {
        layerConfig.collisionObjects[objectId] = {};
      }

      // Save the specific setting
      (layerConfig.collisionObjects[objectId] as Record<string, unknown>)[input] = value;

    });

    // Update the settings tree to reflect the changes
    this.updateSettingsTree();
  }

  // Helper method to find which instance a collision object belongs to
  private findInstanceIdForCollisionObject(objectId: string): string | undefined {
    // Method 1: Check if we have a current instance ID from message processing
    if (this.currentInstanceId) {
      // Verify this instance actually exists in the config
      if (this.renderer.config.layers[this.currentInstanceId]?.layerId === LAYER_ID) {

        return this.currentInstanceId;
      }
    }

    // Method 2: Get instance ID from the renderable's settings path
    const renderable = this.renderables.get(objectId);
    if (renderable?.userData.settingsPath && renderable.userData.settingsPath.length >= 2) {
      const instanceId = renderable.userData.settingsPath[1]!;
      // Verify this instance exists in the config
      if (this.renderer.config.layers[instanceId]?.layerId === LAYER_ID) {

        return instanceId;
      }
    }

    // Method 3: Find the first available planning scene layer
    for (const [instanceId, layerConfig] of Object.entries(this.renderer.config.layers)) {
      if (layerConfig?.layerId === LAYER_ID) {

        return instanceId;
      }
    }

    return undefined;
  }

  public override getSubscriptions(): readonly AnyRendererSubscription[] {
    return [
      {
        type: "schema",
        schemaNames: PLANNING_SCENE_DATATYPES,
        subscription: {
          shouldSubscribe: this.#shouldSubscribe,
          handler: this.handlePlanningSceneMessage.bind(this),
          filterQueue: undefined,
        },
      },
    ];
  }

  #shouldSubscribe = (topic: string): boolean => {
    // Subscribe to topics that are configured in custom layers and have the correct schema
    for (const layerConfig of Object.values(this.renderer.config.layers)) {
      if (layerConfig?.layerId === LAYER_ID) {
        const config = layerConfig as Partial<LayerSettingsPlanningScene>;
        if (config.topic === topic && config.visible === true) {
          // Verify the topic has the correct schema
          const topicInfo = this.renderer.topicsByName?.get(topic);
          if (topicInfo && PLANNING_SCENE_DATATYPES.has(topicInfo.schemaName)) {
            return true;
          }
        }
      }
    }
    return false;
  };

  // Override startFrame to update each collision object individually (Direct Child Manipulation)
  public override startFrame(
    currentTime: bigint,
    renderFrameId: AnyFrameId,
    fixedFrameId: AnyFrameId,
  ): void {

    // Fetch initial scene if we haven't done so yet and we're not already fetching
    // Only fetch if there are visible planning scene layers configured
    const hasVisibleLayers = Object.values(this.renderer.config.layers).some(layer =>
      layer?.layerId === LAYER_ID && layer.visible === true
    );

    if (!this.initialSceneFetched && !this.fetchingInitialScene && hasVisibleLayers) {
      void this.fetchInitialScene();
    } else if (!this.initialSceneFetched && !this.fetchingInitialScene && !hasVisibleLayers) {
      // Clear any existing renderables if there are no visible layers
      if (this.renderables.size > 0) {
        this.removeAllRenderables();
        this.currentScene = undefined;
        this.initialSceneFetched = false;
      }
    }

    // Call the base class implementation to handle standard visibility and transforms
    super.startFrame(currentTime, renderFrameId, fixedFrameId);

    // Add PlanningScene-specific logic after base processing
    this.visibleObjectCount = 0;

    // Performance optimization: Get camera frustum for frustum culling
    const camera = this.renderer.cameraHandler.getActiveCamera();
    this.cameraMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(this.cameraMatrix);

    // Apply additional PlanningScene-specific visibility logic
    for (const collisionObject of this.renderables.values()) {
      // Skip if already invisible from base class processing
      if (!collisionObject.visible) {
        continue;
      }

      // Check if any planning scene layer is visible and shows collision objects
      let layerAllowsVisibility = false;
      for (const layerConfig of Object.values(this.renderer.config.layers)) {
        if (layerConfig?.layerId === LAYER_ID) {
          const config = layerConfig as Partial<LayerSettingsPlanningScene>;
          const layerVisible = config.visible !== false;
          const showCollisionObjects = config.showCollisionObjects !== false;
          if (layerVisible && showCollisionObjects) {
            layerAllowsVisibility = true;
            break; // Found at least one layer that allows visibility
          }
        }
      }

      // Apply layer-level visibility override
      if (!layerAllowsVisibility) {
        collisionObject.visible = false;
        continue;
      }

      // Performance optimization: Frustum culling - skip objects outside camera view
      // Only perform frustum culling if the object has been positioned (has valid world matrix)
      if (!collisionObject.matrixWorldNeedsUpdate) {
        // Create a bounding sphere for the collision object
        const boundingSphere = new THREE.Sphere();
        collisionObject.getWorldPosition(boundingSphere.center);

        // Estimate radius based on collision object size (rough approximation)
        const scale = collisionObject.getWorldScale(new THREE.Vector3());
        boundingSphere.radius = Math.max(scale.x, scale.y, scale.z) * 2; // Conservative estimate

        // Skip if outside frustum
        if (!this.frustum.intersectsSphere(boundingSphere)) {
          continue;
        }
      }

      this.visibleObjectCount++;
    }
  }

  // Handle planning scene messages (differential updates)
  private handlePlanningSceneMessage = (messageEvent: PartialMessageEvent<PlanningScene>): void => {
    const scene = messageEvent.message;
    const topic = messageEvent.topic;

    // Check if there are any planning scene layers configured at all
    const hasConfiguredLayers = Object.values(this.renderer.config.layers).some(layer => layer?.layerId === LAYER_ID);

    if (!hasConfiguredLayers) {
      // No planning scene layers configured, ignore the message
      return;
    }

    // Update message context for creating renderables
    this.topic = topic;
    this.receiveTime = toNanoSec(messageEvent.receiveTime);
    this.messageTime = messageEvent.message.robot_state?.joint_state?.header?.stamp ?
      BigInt(messageEvent.message.robot_state.joint_state.header.stamp.sec ?? 0) * 1000000000n +
      BigInt(messageEvent.message.robot_state.joint_state.header.stamp.nsec ?? 0) :
      toNanoSec(messageEvent.receiveTime);

    // Find the instance ID for this topic
    this.currentInstanceId = this.findInstanceIdForTopic(topic);

    // Load settings from the layer configuration for this topic
    this.settings = this.loadSettingsFromConfig(this.currentInstanceId);

    // Validate message structure
    if (!this.validatePlanningSceneMessage(scene, topic)) {
      return;
    }

    try {
      // Clear any previous message processing errors
      this.renderer.settings.errors.remove(
        ["extensions", PlanningSceneExtension.extensionId, "messageProcessing"],
        MESSAGE_PROCESSING_ERROR
      );

      if (scene.is_diff === true) {
        // Apply differential update to existing scene
        this.applyDifferentialUpdate(scene);
      } else {
        // Replace entire scene
        this.replaceEntireScene(scene);
      }
    } catch (error) {
      const errorMessage = `Failed to process planning scene message from topic "${topic}": ${error instanceof Error ? error.message : String(error)
        }`;
      log.warn(errorMessage);

      // Report error to settings tree
      this.renderer.settings.errors.add(
        ["extensions", PlanningSceneExtension.extensionId, "messageProcessing"],
        MESSAGE_PROCESSING_ERROR,
        errorMessage
      );
    }
  };

  // Fetch initial scene via service call
  private async fetchInitialScene(): Promise<void> {
    // Check if service client is available
    if (!this.serviceClient) {
      const errorMessage = "Service client not available - cannot fetch initial planning scene. Make sure you are connected to a ROS system with the planning scene service.";
      log.warn(errorMessage);

      // Report error to settings tree
      this.renderer.settings.errors.add(
        ["extensions", PlanningSceneExtension.extensionId, "service"],
        SERVICE_ERROR,
        errorMessage
      );
      return;
    }

    // Prevent multiple concurrent service calls
    if (this.fetchingInitialScene) {
      return;
    }

    this.fetchingInitialScene = true;

    // Try to recover settings
    this.currentInstanceId = this.findFirstPlanningSceneInstanceId();
    if (this.currentInstanceId) {
      this.settings = this.loadSettingsFromConfig(this.currentInstanceId);
    }

    try {
      // Clear any previous service errors
      this.renderer.settings.errors.remove(
        ["extensions", PlanningSceneExtension.extensionId, "service"],
        SERVICE_ERROR
      );

      const request: GetPlanningSceneRequest = {
        components: createDefaultPlanningSceneComponents(),
      };

      log.info(`Fetching initial planning scene from service: ${DEFAULT_PLANNING_SCENE_SERVICE}`);

      // Call the service directly

      this.pendingServiceCall = this.serviceClient(DEFAULT_PLANNING_SCENE_SERVICE, request);

      const response = await this.pendingServiceCall as GetPlanningSceneResponse | undefined;

      // Validate the response structure with detailed error messages
      if (response == undefined) {
        throw new Error(`Service '${DEFAULT_PLANNING_SCENE_SERVICE}' returned null or undefined response`);
      }

      if (typeof response !== 'object') {
        throw new Error(`Service '${DEFAULT_PLANNING_SCENE_SERVICE}' returned invalid response type: ${typeof response} (expected object)`);
      }

      if (!Object.prototype.hasOwnProperty.call(response, 'scene')) {
        throw new Error(`Service '${DEFAULT_PLANNING_SCENE_SERVICE}' response missing required 'scene' field. Available fields: ${Object.keys(response).join(', ')}`);
      }

      const scene = response.scene as PartialMessage<PlanningScene> | undefined;

      if (scene == undefined) {
        throw new Error(`Service '${DEFAULT_PLANNING_SCENE_SERVICE}' returned null or undefined scene data`);
      }

      // Validate the scene structure
      const isValid = this.validatePlanningSceneMessage(scene, DEFAULT_PLANNING_SCENE_SERVICE);
      if (!isValid) {
        throw new Error("Invalid planning scene data received from service - see message processing errors for details");
      }

      log.info("Successfully fetched initial planning scene from service");

      // Store the complete scene as baseline for differential updates
      this.currentScene = scene;

      // Process the initial scene (treat as full scene replacement)
      this.replaceEntireScene(scene);

      // Mark as successfully fetched
      this.initialSceneFetched = true;

      // Clear any service errors on success
      this.renderer.settings.errors.remove(
        ["extensions", PlanningSceneExtension.extensionId, "service"],
        SERVICE_ERROR
      );

    } catch (error) {
      let errorMessage: string;

      if (error instanceof Error) {
        // Check for specific error types and provide helpful guidance
        if (error.message.includes('Service client')) {
          errorMessage = `Service client error: ${error.message}`;
        } else if (error.message.includes('not found') || error.message.includes('unavailable')) {
          errorMessage = `Service unavailable: The '${DEFAULT_PLANNING_SCENE_SERVICE}' service is not available. Make sure MoveIt is running and the planning scene service is advertised.`;
        } else {
          errorMessage = `Failed to fetch initial planning scene from '${DEFAULT_PLANNING_SCENE_SERVICE}': ${error.message}`;
        }
      } else {
        errorMessage = `Failed to fetch initial planning scene from '${DEFAULT_PLANNING_SCENE_SERVICE}': ${String(error)}`;
      }

      log.warn(errorMessage);

      // Report error to settings tree
      this.renderer.settings.errors.add(
        ["extensions", PlanningSceneExtension.extensionId, "service"],
        SERVICE_ERROR,
        errorMessage
      );

      // Don't mark as fetched so we can retry later
      this.initialSceneFetched = false;
    } finally {
      this.fetchingInitialScene = false;
      this.pendingServiceCall = undefined; // Clear the pending service call reference

      // Update settings tree to reflect the new status
      this.updateSettingsTree();
    }
  }

  // Apply differential update to existing scene
  private applyDifferentialUpdate(scene: PartialMessage<PlanningScene>): void {
    if (!this.currentScene) {
      // If we don't have a base scene, try to fetch it first
      log.warn("Received differential update without base scene, attempting to fetch initial scene");

      if (!this.fetchingInitialScene && !this.initialSceneFetched) {
        void this.fetchInitialScene();
      } else {
        this.replaceEntireScene(scene);
      }

      return;
    }

    // Update current scene by merging the differential data first
    // This ensures that color information and other data is available during processing
    this.currentScene = this.mergeSceneData(this.currentScene, scene);

    // Process collision objects if present
    if (scene.world?.collision_objects) {
      for (const collisionObject of scene.world.collision_objects) {
        if (collisionObject) {
          this.applyCollisionObjectOperation(collisionObject);
        }
      }
    }

    // Handle color-only updates: if object_colors changed, update existing objects that aren't in collision_objects
    if (scene.object_colors && scene.object_colors.length > 0) {
      const processedObjectIds = new Set(
        scene.world?.collision_objects?.map(obj => obj?.id).filter(Boolean) ?? []
      );

      for (const objectColor of scene.object_colors) {
        if (!objectColor?.id) {
          continue;
        }

        // Only update existing objects that weren't already processed above
        if (!processedObjectIds.has(objectColor.id) && this.renderables.has(objectColor.id)) {
          const existingRenderable = this.renderables.get(objectColor.id);
          if (existingRenderable) {
            // Update the color in userData and trigger visual update
            const sceneColor = this.getObjectColorFromScene(objectColor.id);
            if (sceneColor) {
              existingRenderable.userData.settings.color = sceneColor.color;
              existingRenderable.userData.settings.opacity = this.settings.sceneOpacity * sceneColor.opacity;
              existingRenderable.update(existingRenderable.userData.collisionObject);
            }
          }
        }
      }
    }

    // Process robot state updates if present
    if (scene.robot_state) {
      this.processRobotState(scene.robot_state, scene);
    }

    // Process transform updates if present
    if (scene.fixed_frame_transforms) {
      // Transform updates will be handled in future tasks
      log.info("Transform updates received (not yet implemented)");
    }
  }

  // Process robot state including attached collision objects
  private processRobotState(robotState: PartialMessage<RobotState>, scene?: PartialMessage<PlanningScene>): void {
    // Process attached collision objects
    if (robotState.attached_collision_objects) {
      for (const attachedObject of robotState.attached_collision_objects) {
        if (attachedObject?.object && attachedObject.link_name) {
          this.processAttachedCollisionObject(attachedObject, scene);
        }
      }
    }

    // Joint state processing could be added here in the future
    if (robotState.joint_state) {
      log.info("Joint state processing (not yet implemented)");
    }

    // Multi-DOF joint state processing could be added here in the future
    if (robotState.multi_dof_joint_state) {
      log.info("Multi-DOF joint state processing (not yet implemented)");
    }
  }

  // Process an attached collision object
  private processAttachedCollisionObject(attachedObject: PartialMessage<AttachedCollisionObject>, scene?: PartialMessage<PlanningScene>): void {
    const object = attachedObject.object!;
    const linkName = attachedObject.link_name!;
    const objectId = object.id!;

    // Temporarily store the current scene to preserve color information
    const previousScene = this.currentScene;

    // If we have scene data, temporarily update currentScene to ensure color information is available
    if (scene) {
      this.currentScene = scene;
    }

    try {
      // Create a modified collision object with the link frame
      const attachedCollisionObject: PartialMessage<CollisionObject> = {
        ...object,
        header: {
          ...object.header,
          frame_id: linkName, // Use the link name as the frame instead of the object's original frame
        },
      };

      // Apply the collision object operation (usually ADD for attached objects)
      this.applyCollisionObjectOperation(attachedCollisionObject);

      log.info(`Processed attached collision object '${objectId}' attached to link '${linkName}'`);
    } finally {
      // Restore the previous scene state
      this.currentScene = previousScene;
    }
  }

  // Replace entire scene
  private replaceEntireScene(scene: PartialMessage<PlanningScene>): void {
    // Clear all existing renderables
    this.removeAllRenderables();

    // Store the new scene
    this.currentScene = scene;

    // Process collision objects if present
    if (scene.world?.collision_objects) {
      for (const collisionObject of scene.world.collision_objects) {
        if (collisionObject) {
          this.applyCollisionObjectOperation(collisionObject);
        }
      }
    }

    // Process robot state if present
    if (scene.robot_state) {
      this.processRobotState(scene.robot_state, scene);
    }

    // Process other scene components as needed
    if (scene.fixed_frame_transforms) {
      log.info("Fixed frame transforms processing (not yet implemented)");
    }
  }

  // Merge differential scene data into existing scene
  private mergeSceneData(
    baseScene: PartialMessage<PlanningScene>,
    diffScene: PartialMessage<PlanningScene>
  ): PartialMessage<PlanningScene> {
    // Create a deep copy of the base scene
    const mergedScene: PartialMessage<PlanningScene> = {
      ...baseScene,
      ...diffScene,
    };

    // Handle world data merging
    if (baseScene.world && diffScene.world) {
      mergedScene.world = {
        ...baseScene.world,
        ...diffScene.world,
      };

      // For collision objects, we need special handling since they can be added/removed/modified
      if (diffScene.world.collision_objects && diffScene.world.collision_objects.length > 0) {
        // The collision objects in the diff will be processed by applyCollisionObjectOperation
        // which will handle ADD/REMOVE/APPEND/MOVE operations appropriately
        // For now, we keep the base collision objects and let the operation handler manage changes
        mergedScene.world.collision_objects = baseScene.world.collision_objects ?? [];
      }
    }

    // Handle robot state merging
    if (baseScene.robot_state && diffScene.robot_state) {
      mergedScene.robot_state = {
        ...baseScene.robot_state,
        ...diffScene.robot_state,
      };
    }

    // Handle object_colors merging - merge colors instead of replacing the entire array
    if (baseScene.object_colors || diffScene.object_colors) {
      const baseColors = baseScene.object_colors ?? [];
      const diffColors = diffScene.object_colors ?? [];

      // Create a map of existing colors by object ID for efficient lookup
      const colorMap = new Map<string, typeof baseColors[0]>();

      // Add base colors to the map
      for (const colorEntry of baseColors) {
        if (colorEntry?.id) {
          colorMap.set(colorEntry.id, colorEntry);
        }
      }

      // Add/update with diff colors (this will overwrite existing colors for the same object ID)
      for (const colorEntry of diffColors) {
        if (colorEntry?.id) {
          colorMap.set(colorEntry.id, colorEntry);
        }
      }

      // Convert back to array
      mergedScene.object_colors = Array.from(colorMap.values());
    }

    return mergedScene;
  }

  // Validate planning scene message structure
  private validatePlanningSceneMessage(scene: PartialMessage<PlanningScene>, topic: string): boolean {
    try {
      // Since scene is a PartialMessage, it should always be defined, but we can check for empty object
      if (Object.keys(scene).length === 0) {
        throw new Error(`Received empty planning scene message from topic "${topic}"`);
      }

      // Check for required fields based on message type
      if (scene.is_diff === true) {
        // For differential updates, we need at least some content to apply
        const hasCollisionObjects = scene.world?.collision_objects != undefined && scene.world.collision_objects.length > 0;
        const hasRobotState = scene.robot_state != undefined;
        const hasTransforms = scene.fixed_frame_transforms != undefined && scene.fixed_frame_transforms.length > 0;

        if (!hasCollisionObjects && !hasRobotState && !hasTransforms) {
          throw new Error(`Received differential planning scene message with no content from topic "${topic}"`);
        }

        // Validate collision objects in differential updates
        if (hasCollisionObjects) {
          this.validateCollisionObjects(scene.world!.collision_objects as (PartialMessage<CollisionObject> | undefined)[], topic);
        }
      } else {
        // For full scene updates, we expect a world object (even if empty)
        if (scene.world == undefined) {
          throw new Error(`Received full planning scene message without world data from topic "${topic}"`);
        }

        // Validate collision objects in full scene updates
        if (scene.world.collision_objects && scene.world.collision_objects.length > 0) {
          this.validateCollisionObjects(scene.world.collision_objects as (PartialMessage<CollisionObject> | undefined)[], topic);
        }
      }

      return true;
    } catch (error) {
      const errorMessage = `Message validation failed for topic "${topic}": ${error instanceof Error ? error.message : String(error)}`;
      log.warn(errorMessage);

      // Report validation error to settings tree
      this.renderer.settings.errors.add(
        ["extensions", PlanningSceneExtension.extensionId, "messageProcessing"],
        MESSAGE_PROCESSING_ERROR,
        errorMessage
      );

      return false;
    }
  }

  // Validate collision objects structure
  private validateCollisionObjects(collisionObjects: (PartialMessage<CollisionObject> | undefined)[], _topic: string): void {
    for (let i = 0; i < collisionObjects.length; i++) {
      const obj = collisionObjects[i];
      if (obj == undefined) {
        throw new Error(`Collision object at index ${i} is null or undefined`);
      }

      if (obj.id == undefined || typeof obj.id !== 'string') {
        throw new Error(`Collision object at index ${i} has invalid or missing 'id' field`);
      }

      if (obj.operation == undefined || typeof obj.operation !== 'number') {
        throw new Error(`Collision object '${obj.id}' has invalid or missing 'operation' field`);
      }

      // Validate operation-specific requirements
      if (obj.operation === CollisionObjectOperation.ADD || obj.operation === CollisionObjectOperation.MOVE) {
        if (obj.header?.frame_id == undefined) {
          throw new Error(`Collision object '${obj.id}' with ${CollisionObjectOperation[obj.operation]} operation requires header.frame_id`);
        }

        if (obj.pose == undefined) {
          throw new Error(`Collision object '${obj.id}' with ${CollisionObjectOperation[obj.operation]} operation requires pose`);
        }
      }

      // Validate geometry data
      if (obj.primitives != undefined) {
        this.validatePrimitives(obj.primitives, obj.id);
      }

      if (obj.meshes != undefined) {
        this.validateMeshes(obj.meshes, obj.id);
      }
    }
  }

  // Validate primitive shapes
  private validatePrimitives(primitives: (PartialMessage<SolidPrimitive> | undefined)[], objectId: string): void {
    for (let i = 0; i < primitives.length; i++) {
      const primitive = primitives[i];
      if (primitive == undefined) {
        throw new Error(`Primitive at index ${i} in collision object '${objectId}' is null or undefined`);
      }

      if (primitive.type == undefined || typeof primitive.type !== 'number') {
        throw new Error(`Primitive at index ${i} in collision object '${objectId}' has invalid or missing 'type' field`);
      }

      if (primitive.dimensions == undefined || !Array.isArray(primitive.dimensions)) {
        throw new Error(`Primitive at index ${i} in collision object '${objectId}' has invalid or missing 'dimensions' field`);
      }

      // Validate dimensions based on primitive type
      switch (primitive.type) {
        case SolidPrimitiveType.BOX:
          if (primitive.dimensions.length < 3) {
            throw new Error(`Box primitive at index ${i} in collision object '${objectId}' requires 3 dimensions [x, y, z]`);
          }
          break;
        case SolidPrimitiveType.SPHERE:
          if (primitive.dimensions.length < 1) {
            throw new Error(`Sphere primitive at index ${i} in collision object '${objectId}' requires 1 dimension [radius]`);
          }
          break;
        case SolidPrimitiveType.CYLINDER:
        case SolidPrimitiveType.CONE:
          if (primitive.dimensions.length < 2) {
            throw new Error(`${primitive.type === SolidPrimitiveType.CYLINDER ? 'Cylinder' : 'Cone'} primitive at index ${i} in collision object '${objectId}' requires 2 dimensions [height, radius]`);
          }
          break;
      }

      // Validate dimension values are positive
      for (let j = 0; j < primitive.dimensions.length; j++) {
        const dim = primitive.dimensions[j];
        if (typeof dim !== 'number' || dim <= 0) {
          throw new Error(`Primitive at index ${i} in collision object '${objectId}' has invalid dimension at index ${j}: ${dim} (must be positive number)`);
        }
      }
    }
  }

  // Validate mesh data
  private validateMeshes(meshes: (PartialMessage<Mesh> | undefined)[], objectId: string): void {
    for (let i = 0; i < meshes.length; i++) {
      const mesh = meshes[i];
      if (mesh == undefined) {
        throw new Error(`Mesh at index ${i} in collision object '${objectId}' is null or undefined`);
      }

      if (mesh.vertices == undefined || !Array.isArray(mesh.vertices)) {
        throw new Error(`Mesh at index ${i} in collision object '${objectId}' has invalid or missing 'vertices' field`);
      }

      if (mesh.triangles == undefined || !Array.isArray(mesh.triangles)) {
        throw new Error(`Mesh at index ${i} in collision object '${objectId}' has invalid or missing 'triangles' field`);
      }

      if (mesh.vertices.length === 0) {
        throw new Error(`Mesh at index ${i} in collision object '${objectId}' has no vertices`);
      }

      if (mesh.triangles.length === 0) {
        throw new Error(`Mesh at index ${i} in collision object '${objectId}' has no triangles`);
      }

      // Validate triangle indices
      for (let j = 0; j < mesh.triangles.length; j++) {
        const triangle = mesh.triangles[j];
        if (triangle?.vertex_indices == undefined || triangle.vertex_indices.length !== 3) {
          throw new Error(`Triangle at index ${j} in mesh ${i} of collision object '${objectId}' has invalid vertex_indices (must be array of 3 indices)`);
        }

        for (let k = 0; k < 3; k++) {
          const vertexIndex = triangle.vertex_indices[k];
          if (typeof vertexIndex !== 'number' || vertexIndex < 0 || vertexIndex >= mesh.vertices.length) {
            throw new Error(`Triangle at index ${j} in mesh ${i} of collision object '${objectId}' has invalid vertex index ${vertexIndex} (must be 0-${mesh.vertices.length - 1})`);
          }
        }
      }
    }
  }

  // Apply collision object operations (ADD, REMOVE, APPEND, MOVE)
  private applyCollisionObjectOperation(object: PartialMessage<CollisionObject>): void {
    // Validate required fields
    if (object.id == undefined) {
      const errorMessage = "Collision object missing required 'id' field, cannot process operation";
      log.warn(errorMessage);

      // Report error to settings tree (use a generic path since we don't have an ID)
      this.renderer.settings.errors.add(
        ["extensions", PlanningSceneExtension.extensionId, "messageProcessing"],
        MESSAGE_PROCESSING_ERROR,
        errorMessage
      );
      return;
    }

    if (object.operation == undefined) {
      const errorMessage = `Collision object '${object.id}' missing required 'operation' field, cannot process`;
      log.warn(errorMessage);

      // Report error to settings tree
      this.renderer.settings.errors.add(
        ["extensions", PlanningSceneExtension.extensionId, "collisionObjects", object.id],
        MESSAGE_PROCESSING_ERROR,
        errorMessage
      );
      return;
    }

    const objectId = object.id;
    const operation = object.operation;

    // Differential update - only process changed objects for performance optimization
    if (operation === CollisionObjectOperation.ADD || operation === CollisionObjectOperation.APPEND || operation === CollisionObjectOperation.MOVE) {
      const fullObject = object as CollisionObject;
      if (!this.hasObjectChanged(fullObject)) {
        // Object hasn't changed, skip processing
        return;
      }
    }

    try {
      // Clear any previous errors for this collision object
      this.renderer.settings.errors.remove(
        ["extensions", PlanningSceneExtension.extensionId, "collisionObjects", objectId],
        MESSAGE_PROCESSING_ERROR
      );

      switch (operation) {
        case CollisionObjectOperation.ADD: {
          this.handleAddOperation(object);
          break;
        }
        case CollisionObjectOperation.REMOVE: {
          this.handleRemoveOperation(objectId);
          break;
        }
        case CollisionObjectOperation.APPEND: {
          this.handleAppendOperation(object);
          break;
        }
        case CollisionObjectOperation.MOVE: {
          this.handleMoveOperation(object);
          break;
        }
        default: {
          const errorMessage = `Unknown collision object operation: ${operation} (${CollisionObjectOperation[operation] ?? 'UNKNOWN'}) for object '${objectId}'. Valid operations are: ADD (${CollisionObjectOperation.ADD}), REMOVE (${CollisionObjectOperation.REMOVE}), APPEND (${CollisionObjectOperation.APPEND}), MOVE (${CollisionObjectOperation.MOVE})`;
          log.warn(errorMessage);

          // Report error to settings tree
          this.renderer.settings.errors.add(
            ["extensions", PlanningSceneExtension.extensionId, "collisionObjects", objectId],
            MESSAGE_PROCESSING_ERROR,
            errorMessage
          );
          break;
        }
      }
    } catch (error) {
      const operationName = CollisionObjectOperation[operation] || `UNKNOWN(${operation})`;
      const errorMessage = `Failed to apply ${operationName} operation for collision object '${objectId}': ${error instanceof Error ? error.message : String(error)}`;
      log.warn(errorMessage);

      // Report error to settings tree
      this.renderer.settings.errors.add(
        ["extensions", PlanningSceneExtension.extensionId, "collisionObjects", objectId],
        MESSAGE_PROCESSING_ERROR,
        errorMessage
      );
    }
  }

  // Handle ADD operation - creates new CollisionObjectRenderable instances
  private handleAddOperation(object: PartialMessage<CollisionObject>): void {
    const objectId = object.id!;

    // Validate required fields for ADD operation
    if (object.header?.frame_id == undefined) {
      throw new Error(`ADD operation for object '${objectId}' requires header.frame_id to specify the coordinate frame`);
    }

    if (object.pose == undefined) {
      throw new Error(`ADD operation for object '${objectId}' requires pose to specify position and orientation`);
    }

    try {
      // Validate pose values
      const pose = object.pose;
      if (pose.position && (
        (pose.position.x != undefined && !isFinite(pose.position.x)) ||
        (pose.position.y != undefined && !isFinite(pose.position.y)) ||
        (pose.position.z != undefined && !isFinite(pose.position.z))
      )) {
        throw new Error(`ADD operation for object '${objectId}' has invalid position values: [${pose.position.x ?? 'undefined'}, ${pose.position.y ?? 'undefined'}, ${pose.position.z ?? 'undefined'}]`);
      }

      if (pose.orientation && (
        (pose.orientation.x != undefined && !isFinite(pose.orientation.x)) ||
        (pose.orientation.y != undefined && !isFinite(pose.orientation.y)) ||
        (pose.orientation.z != undefined && !isFinite(pose.orientation.z)) ||
        (pose.orientation.w != undefined && !isFinite(pose.orientation.w))
      )) {
        throw new Error(`ADD operation for object '${objectId}' has invalid orientation values: [${pose.orientation.x ?? 'undefined'}, ${pose.orientation.y ?? 'undefined'}, ${pose.orientation.z ?? 'undefined'}, ${pose.orientation.w ?? 'undefined'}]`);
      }

      // Cast to full type after validation
      const fullObject = object as CollisionObject;

      // Count geometry for logging
      const primitiveCount = fullObject.primitives.length;
      const meshCount = fullObject.meshes.length;
      const planeCount = fullObject.planes.length;
      const totalShapes = primitiveCount + meshCount + planeCount;

      // Remove existing object if it exists (ADD replaces existing objects)
      if (this.renderables.has(objectId)) {
        log.info(`Replacing existing collision object '${objectId}' with ADD operation`);
        this.handleRemoveOperation(objectId);
      }

      // Extract color from planning scene message, falling back to default
      const sceneColor = this.getObjectColorFromScene(objectId);
      const objectColor = sceneColor?.color ?? this.settings.defaultColor;

      // Get saved settings for this collision object from configuration
      const instanceId = this.findInstanceIdForCollisionObject(objectId) ?? this.currentInstanceId;
      const layerConfig = instanceId ? this.renderer.config.layers[instanceId] as Partial<LayerSettingsPlanningScene> | undefined : undefined;
      const savedObjectSettings = layerConfig?.collisionObjects?.[objectId];

      // Apply scene opacity multiplicatively: sceneOpacity * messageOpacity (or 1.0 if no message opacity)
      const sceneOpacity = this.settings.sceneOpacity;
      const messageOpacity = sceneColor?.opacity ?? 1.0;
      const finalOpacity = sceneOpacity * messageOpacity;

      // Create user data for the collision object
      const userData: CollisionObjectUserData = {
        topic: this.topic,
        receiveTime: this.receiveTime,
        messageTime: this.messageTime,
        frameId: fullObject.header.frame_id,
        pose: fullObject.pose,
        settingsPath: ["layers", instanceId ?? "unknown", "collisionObjects", objectId],
        settings: {
          visible: savedObjectSettings?.visible ?? true,
          color: savedObjectSettings?.color ?? objectColor, // Use saved color, then scene color, then default
          opacity: finalOpacity, // Use scene opacity * message opacity
          showPrimitives: savedObjectSettings?.showPrimitives ?? true,
          showMeshes: savedObjectSettings?.showMeshes ?? true,
          showPlanes: savedObjectSettings?.showPlanes ?? true,
        },
        collisionObject: fullObject,
        shapes: new Map<string, Renderable>(),
      };

      // Create the collision object renderable
      const renderable = new CollisionObjectRenderable(objectId, this.renderer, userData);

      // Performance optimization: Set extension reference for performance optimizations
      renderable.setExtension({
        loadMeshResource: this.loadMeshResource.bind(this),
        getSharedGeometry: this.getSharedGeometry.bind(this),
        getSharedMaterial: this.getSharedMaterial.bind(this),
      });

      // Update the renderable with the collision object data
      renderable.update(fullObject);

      // Set visibility based on extension and object settings
      renderable.visible = this.settings.showCollisionObjects && userData.settings.visible;

      // Add to the scene
      this.add(renderable);
      this.renderables.set(objectId, renderable);

      log.info(`Added collision object '${objectId}' in frame '${fullObject.header.frame_id}' with ${totalShapes} shapes (${primitiveCount} primitives, ${meshCount} meshes, ${planeCount} planes)`);
    } catch (error) {
      throw new Error(`ADD operation for object '${objectId}' failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Handle REMOVE operation - disposes and removes renderables
  private handleRemoveOperation(objectId: string): void {
    const renderable = this.renderables.get(objectId);
    if (!renderable) {
      // Not an error - object might not exist or already removed
      log.info(`Collision object '${objectId}' not found for REMOVE operation (may already be removed)`);
      return;
    }

    // Remove from scene and dispose resources
    this.remove(renderable);
    renderable.dispose();
    this.renderables.delete(objectId);

    // Clean up object hash when removed for performance optimization
    this.objectHashes.delete(objectId);

    // Clear any errors for this object
    this.renderer.settings.errors.clearPath(
      ["extensions", PlanningSceneExtension.extensionId, "collisionObjects", objectId]
    );

    log.info(`Removed collision object '${objectId}'`);
  }

  // Handle MOVE operation - updates poses without changing geometry
  private handleMoveOperation(object: PartialMessage<CollisionObject>): void {
    const objectId = object.id!;
    const renderable = this.renderables.get(objectId);

    if (renderable == undefined) {
      throw new Error(`Cannot MOVE collision object '${objectId}': object not found. Make sure the object was previously added with an ADD operation.`);
    }

    // Validate that geometry arrays are empty for MOVE operation
    const primitiveCount = object.primitives?.length ?? 0;
    const meshCount = object.meshes?.length ?? 0;
    const planeCount = object.planes?.length ?? 0;
    const hasGeometry = primitiveCount > 0 || meshCount > 0 || planeCount > 0;

    if (hasGeometry) {
      throw new Error(`MOVE operation for object '${objectId}' must have empty geometry arrays. Found ${primitiveCount} primitives, ${meshCount} meshes, ${planeCount} planes. Use APPEND operation to add geometry or ADD operation to replace the entire object.`);
    }

    // Validate required fields for MOVE operation
    if (object.header?.frame_id == undefined) {
      throw new Error(`MOVE operation for object '${objectId}' requires header.frame_id to specify the target coordinate frame`);
    }

    if (object.pose == undefined) {
      throw new Error(`MOVE operation for object '${objectId}' requires pose to specify the new position and orientation`);
    }

    try {
      // Validate pose values
      const pose = object.pose;
      if (pose.position && (
        (pose.position.x != undefined && !isFinite(pose.position.x)) ||
        (pose.position.y != undefined && !isFinite(pose.position.y)) ||
        (pose.position.z != undefined && !isFinite(pose.position.z))
      )) {
        throw new Error(`MOVE operation for object '${objectId}' has invalid position values: [${pose.position.x ?? 'undefined'}, ${pose.position.y ?? 'undefined'}, ${pose.position.z ?? 'undefined'}]`);
      }

      if (pose.orientation && (
        (pose.orientation.x != undefined && !isFinite(pose.orientation.x)) ||
        (pose.orientation.y != undefined && !isFinite(pose.orientation.y)) ||
        (pose.orientation.z != undefined && !isFinite(pose.orientation.z)) ||
        (pose.orientation.w != undefined && !isFinite(pose.orientation.w))
      )) {
        throw new Error(`MOVE operation for object '${objectId}' has invalid orientation values: [${pose.orientation.x ?? 'undefined'}, ${pose.orientation.y ?? 'undefined'}, ${pose.orientation.z ?? 'undefined'}, ${pose.orientation.w ?? 'undefined'}]`);
      }

      // Update the pose and frame information without changing geometry
      renderable.userData.pose = this.ensureFullPose(object.pose);
      renderable.userData.frameId = object.header.frame_id!;
      renderable.userData.collisionObject = {
        ...renderable.userData.collisionObject,
        header: this.ensureFullHeader(object.header),
        pose: this.ensureFullPose(object.pose),
      };

      // Update color and opacity from planning scene if available (colors may have changed)
      const sceneColor = this.getObjectColorFromScene(objectId);
      if (sceneColor) {
        renderable.userData.settings.color = sceneColor.color;
        // Apply scene opacity multiplicatively
        const messageOpacity = sceneColor.opacity;
        renderable.userData.settings.opacity = this.settings.sceneOpacity * messageOpacity;
        // Trigger visual update to apply new colors
        renderable.update(renderable.userData.collisionObject);
      }

      log.info(`Moved collision object '${objectId}' to new pose in frame '${object.header.frame_id}'`);
    } catch (error) {
      throw new Error(`MOVE operation for object '${objectId}' failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Handle APPEND operation - adds shapes to existing objects
  private handleAppendOperation(object: PartialMessage<CollisionObject>): void {
    const objectId = object.id!;
    const renderable = this.renderables.get(objectId);

    if (renderable == undefined) {
      throw new Error(`Cannot APPEND to collision object '${objectId}': object not found. Make sure the object was previously added with an ADD operation.`);
    }

    // Get the existing collision object data
    const existingObject = renderable.userData.collisionObject;

    // Validate that we have something to append
    const newPrimitives = object.primitives?.filter((p) => p != undefined) ?? [];
    const newPrimitivePoses = object.primitive_poses?.filter((p) => p != undefined) ?? [];
    const newMeshes = object.meshes?.filter((m) => m != undefined) ?? [];
    const newMeshPoses = object.mesh_poses?.filter((p) => p != undefined) ?? [];
    const newPlanes = object.planes?.filter((p) => p != undefined) ?? [];
    const newPlanePoses = object.plane_poses?.filter((p) => p != undefined) ?? [];

    const totalNewShapes = newPrimitives.length + newMeshes.length + newPlanes.length;
    if (totalNewShapes === 0) {
      throw new Error(`APPEND operation for object '${objectId}' has no geometry to append. Provide at least one primitive, mesh, or plane.`);
    }

    // Validate pose arrays match geometry arrays
    if (newPrimitives.length > 0 && newPrimitivePoses.length > 0 && newPrimitives.length !== newPrimitivePoses.length) {
      throw new Error(`APPEND operation for object '${objectId}': primitive count (${newPrimitives.length}) does not match primitive pose count (${newPrimitivePoses.length})`);
    }

    if (newMeshes.length > 0 && newMeshPoses.length > 0 && newMeshes.length !== newMeshPoses.length) {
      throw new Error(`APPEND operation for object '${objectId}': mesh count (${newMeshes.length}) does not match mesh pose count (${newMeshPoses.length})`);
    }

    if (newPlanes.length > 0 && newPlanePoses.length > 0 && newPlanes.length !== newPlanePoses.length) {
      throw new Error(`APPEND operation for object '${objectId}': plane count (${newPlanes.length}) does not match plane pose count (${newPlanePoses.length})`);
    }

    try {
      const mergedObject: CollisionObject = {
        ...existingObject,
        // Append new primitives
        primitives: [...existingObject.primitives, ...newPrimitives] as CollisionObject['primitives'],
        primitive_poses: [...existingObject.primitive_poses, ...newPrimitivePoses] as CollisionObject['primitive_poses'],
        // Append new meshes
        meshes: [...existingObject.meshes, ...newMeshes] as CollisionObject['meshes'],
        mesh_poses: [...existingObject.mesh_poses, ...newMeshPoses] as CollisionObject['mesh_poses'],
        // Append new planes
        planes: [...existingObject.planes, ...newPlanes] as CollisionObject['planes'],
        plane_poses: [...existingObject.plane_poses, ...newPlanePoses] as CollisionObject['plane_poses'],
        // Update header and pose if provided
        header: object.header ? this.ensureFullHeader(object.header) : existingObject.header,
        pose: object.pose ? this.ensureFullPose(object.pose) : existingObject.pose
      };

      // Update color and opacity from planning scene if available (colors may have changed)
      const sceneColor = this.getObjectColorFromScene(objectId);
      if (sceneColor) {
        renderable.userData.settings.color = sceneColor.color;
        // Apply scene opacity multiplicatively
        const messageOpacity = sceneColor.opacity;
        renderable.userData.settings.opacity = this.settings.sceneOpacity * messageOpacity;
      }

      // Update the renderable with the merged data
      renderable.update(mergedObject);

      const addedPrimitives = newPrimitives.length;
      const addedMeshes = newMeshes.length;
      const addedPlanes = newPlanes.length;
      const totalExisting = existingObject.primitives.length + existingObject.meshes.length + existingObject.planes.length;

      log.info(`Appended to collision object '${objectId}': ${addedPrimitives} primitives, ${addedMeshes} meshes, ${addedPlanes} planes (total shapes: ${totalExisting + totalNewShapes})`);
    } catch (error) {
      throw new Error(`APPEND operation for object '${objectId}' failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public override removeAllRenderables(): void {
    for (const renderable of this.renderables.values()) {
      this.remove(renderable);
      renderable.dispose();
    }
    this.renderables.clear();

    // Clear all object hashes when removing all renderables for performance optimization
    this.objectHashes.clear();

    // Clear all collision object errors
    this.renderer.settings.errors.clearPath(
      ["extensions", PlanningSceneExtension.extensionId, "collisionObjects"]
    );
  }

  // Public method to manually retry fetching initial scene
  public retryFetchInitialScene(): void {
    this.initialSceneFetched = false;
    this.fetchingInitialScene = false;
    void this.fetchInitialScene();
  }

  // Public method to check if initial scene has been fetched
  public hasInitialScene(): boolean {
    return this.initialSceneFetched && this.currentScene != undefined;
  }



  // Get performance statistics for monitoring optimization effectiveness
  public getPerformanceStats(): {
    totalObjects: number;
    visibleObjects: number;
    hiddenObjects: number;
    cachedGeometries: number;
    cachedMaterials: number;
    cachedMeshes: number;
    objectHashes: number;
    cullingSavings: string;
  } {
    const hiddenObjects = this.renderables.size - this.visibleObjectCount;
    const cullingSavings = this.renderables.size > 0
      ? `${Math.round((hiddenObjects / this.renderables.size) * 100)}%`
      : "0%";

    return {
      totalObjects: this.renderables.size,
      visibleObjects: this.visibleObjectCount,
      hiddenObjects,
      cachedGeometries: this.geometryCache.size,
      cachedMaterials: this.materialCache.size,
      cachedMeshes: this.meshResourceCache.size,
      objectHashes: this.objectHashes.size,
      cullingSavings,
    };
  }

  // Helper function to ensure a partial pose becomes a full pose
  private ensureFullPose(partialPose: PartialMessage<CollisionObject>['pose']): Pose {
    if (!partialPose) {
      throw new Error("Pose is required");
    }

    return {
      position: {
        x: partialPose.position?.x ?? 0,
        y: partialPose.position?.y ?? 0,
        z: partialPose.position?.z ?? 0,
      },
      orientation: {
        x: partialPose.orientation?.x ?? 0,
        y: partialPose.orientation?.y ?? 0,
        z: partialPose.orientation?.z ?? 0,
        w: partialPose.orientation?.w ?? 1,
      },
    };
  }

  // Helper function to ensure a partial header becomes a full header
  private ensureFullHeader(partialHeader: PartialMessage<CollisionObject>['header']): Header {
    if (!partialHeader) {
      throw new Error("Header is required");
    }

    return {
      frame_id: partialHeader.frame_id ?? "",
      stamp: {
        sec: partialHeader.stamp?.sec ?? 0,
        nsec: partialHeader.stamp?.nsec ?? 0,
      },
      seq: partialHeader.seq,
    };
  }

  #updatePlanningSceneLayer(instanceId: string, settings: Partial<LayerSettingsPlanningScene> | undefined): void {
    // Handle deletes
    if (settings == undefined) {
      // Remove all renderables associated with this layer instance
      // Since planning scene extension shares renderables across all instances,
      // we need to check if this is the last instance before removing renderables
      const remainingInstances = Object.entries(this.renderer.config.layers)
        .filter(([id, config]) => id !== instanceId && config?.layerId === LAYER_ID);

      if (remainingInstances.length === 0) {
        // This was the last planning scene layer, remove all renderables
        for (const renderable of this.renderables.values()) {
          renderable.dispose();
          this.remove(renderable);
        }
        this.renderables.clear();
        this.currentScene = undefined;
        this.initialSceneFetched = false;
        this.fetchingInitialScene = false;
      }

      return;
    }

    // If there are existing renderables, update their settings from the layer config
    this.#reloadRenderableSettings();
  }

  // Helper method to reload settings for existing renderables from layer config
  #reloadRenderableSettings(): void {
    // Also reload the main settings to get updated scene opacity
    const currentInstanceId = this.currentInstanceId ?? this.findFirstPlanningSceneInstanceId();
    if (currentInstanceId) {
      this.settings = this.loadSettingsFromConfig(currentInstanceId);
    }

    for (const [objectId, renderable] of this.renderables.entries()) {
      // Find the instance ID for this renderable
      const instanceId = this.findInstanceIdForCollisionObject(objectId);
      if (!instanceId) {
        continue;
      }

      // Get the layer config
      const layerConfig = this.renderer.config.layers[instanceId] as Partial<LayerSettingsPlanningScene> | undefined;
      const savedObjectSettings = layerConfig?.collisionObjects?.[objectId];

      if (savedObjectSettings) {
        // Update the renderable's settings with saved values
        const settings = renderable.userData.settings;
        if (savedObjectSettings.visible != undefined) {
          settings.visible = savedObjectSettings.visible;
        }
        if (savedObjectSettings.color != undefined) {
          settings.color = savedObjectSettings.color;
        }
        if (savedObjectSettings.showPrimitives != undefined) {
          settings.showPrimitives = savedObjectSettings.showPrimitives;
        }
        if (savedObjectSettings.showMeshes != undefined) {
          settings.showMeshes = savedObjectSettings.showMeshes;
        }
        if (savedObjectSettings.showPlanes != undefined) {
          settings.showPlanes = savedObjectSettings.showPlanes;
        }
      }

      // Recalculate opacity based on updated scene opacity
      const sceneColor = this.getObjectColorFromScene(objectId);
      const messageOpacity = sceneColor?.opacity ?? 1.0;
      renderable.userData.settings.opacity = this.settings.sceneOpacity * messageOpacity;

      // Update the visual representation
      renderable.update(renderable.userData.collisionObject);
    }
  }

  // Helper method to find the first planning scene instance ID
  private findFirstPlanningSceneInstanceId(): string | undefined {

    for (const [instanceId, layerConfig] of Object.entries(this.renderer.config.layers)) {
      if (layerConfig?.layerId === LAYER_ID) {
        return instanceId;
      }
    }
    return undefined;
  }

  // Custom layer handlers
  #handleAddPlanningScene = (instanceId: string): void => {
    // Check if a planning scene already exists
    const existingPlanningSceneId = this.findFirstPlanningSceneInstanceId();
    if (existingPlanningSceneId) {
      const errorMessage = "Only one planning scene can exist at a time. Please delete the existing planning scene before adding a new one.";
      log.warn(errorMessage);

      // Display error message to user if available
      if (this.renderer.displayTemporaryError) {
        try {
          // Use setTimeout to ensure the error is displayed after the current execution context
          setTimeout(() => {
            this.renderer.displayTemporaryError!(errorMessage);
            log.info("Successfully called displayTemporaryError with message:", errorMessage);
          }, 0);
        } catch (error) {
          log.error("Error calling displayTemporaryError:", error);
        }
      } else {
        log.warn("No displayTemporaryError function available to display error message");
      }

      // Also add the error to the settings tree to ensure it's visible in the UI
      this.renderer.settings.errors.add(
        ["extensions", PlanningSceneExtension.extensionId, "planningSceneCreation"],
        "PLANNING_SCENE_EXISTS",
        errorMessage
      );

      return; // Exit early without creating a new planning scene
    }

    const config: LayerSettingsPlanningScene = {
      ...DEFAULT_CUSTOM_SETTINGS,
      instanceId,
      topic: this.topic || DEFAULT_PLANNING_SCENE_TOPIC, // Use current topic or default
    };

    // Add this instance to the config
    this.renderer.updateConfig((draft) => {
      const maxOrderLayer = _.maxBy(Object.values(draft.layers), (layer) => layer?.order);
      const order = 1 + (maxOrderLayer?.order ?? 0);
      draft.layers[instanceId] = { ...config, order };
    });

    // Clear any previous planning scene creation errors
    this.renderer.settings.errors.remove(
      ["extensions", PlanningSceneExtension.extensionId, "planningSceneCreation"],
      "PLANNING_SCENE_EXISTS"
    );

    // Update the planning scene layer
    this.#updatePlanningSceneLayer(instanceId, config);

    // Update the settings tree
    this.updateSettingsTree();
    this.renderer.updateCustomLayersCount();
  };

  #handleLayerSettingsAction = (action: SettingsTreeAction): void => {
    const path = action.payload.path;

    // Handle menu actions (duplicate / delete / retry service)
    if (action.action === "perform-node-action" && path.length === 2) {
      const instanceId = path[1]!;
      if (action.payload.id === "delete") {
        // Remove this instance from the config
        this.renderer.updateConfig((draft) => {
          delete draft.layers[instanceId];
        });

        // Clear any planning scene creation errors since we're deleting a scene
        this.renderer.settings.errors.remove(
          ["extensions", PlanningSceneExtension.extensionId, "planningSceneCreation"],
          "PLANNING_SCENE_EXISTS"
        );

        // Remove the layer
        this.#updatePlanningSceneLayer(instanceId, undefined);

        // Update the settings tree
        this.updateSettingsTree();
        this.renderer.updateCustomLayersCount();
      } else if (action.payload.id === "refetch") {
        // Refetch the planning scene
        this.retryFetchInitialScene();
      }
    } else if (action.action === "perform-node-action") {
      // Handle service retry action for custom layers
      const { id } = action.payload;
      if (path.length === 3 && path[2] === "service" && (id === "retryService" || id === "refetchService")) {
        this.retryFetchInitialScene();
      }
    } else {
      const value = action.payload.value;

      if (path.length === 3 && path[2] === "visible") {
        // Layer visibility toggle - path is ["layers", instanceId, "visible"]
        this.saveSetting(path, value);
        // Visibility will be recalculated in the next frame by startFrame()
        // No need to manually update all objects here
        this.updateSettingsTree();
      } else if (path.length === 3) {
        // Layer field settings (defaultColor, sceneOpacity, etc.)
        this.#handleLayerSettingsUpdate(action);
      } else if (path.length === 4 && path[2] === "collisionObjects") {
        // Collision object visibility toggle (eye icon) - path is ["layers", instanceId, "collisionObjects", objectId]
        const objectId = path[3];
        if (typeof objectId === "string") {
          this.handleCollisionObjectSettingsUpdate(objectId, "visible", value);
        }
      } else if (path.length === 5 && path[2] === "collisionObjects") {
        // Collision object field settings - path is ["layers", instanceId, "collisionObjects", objectId, fieldName]
        const objectId = path[3];
        const fieldName = path[4]; // The actual field name is in the path, not the input

        if (typeof objectId === "string" && typeof fieldName === "string") {
          // For eye icon toggles, input is "boolean" but fieldName is "visible"
          this.handleCollisionObjectSettingsUpdate(objectId, fieldName, value);
        }
      }
    }
  };

  #handleLayerSettingsUpdate = (action: { action: "update" } & SettingsTreeAction): void => {
    const path = action.payload.path;

    if (path.length !== 3) {
      return; // Doesn't match the pattern of ["layers", instanceId, field]
    }

    const { value } = action.payload;
    const fieldName = path[2]; // The field name is the third element in the path

    // Save the setting to config first
    this.saveSetting(path, value);

    // Handle specific settings that need special processing
    if (fieldName === "defaultColor") {
      // Update all collision objects that don't have colors in the planning scene message
      for (const renderable of this.renderables.values()) {
        const objectId = renderable.userData.collisionObject.id;
        const sceneColor = this.getObjectColorFromScene(objectId);

        // Only update if no color is specified in the planning scene message
        if (!sceneColor) {
          renderable.userData.settings.color = value as string;
          // Trigger visual update
          renderable.update(renderable.userData.collisionObject);
        }
      }
    } else if (fieldName === "sceneOpacity") {
      // Update all collision objects with new scene opacity
      const newSceneOpacity = value as number;

      for (const renderable of this.renderables.values()) {
        const objectId = renderable.userData.collisionObject.id;
        const sceneColor = this.getObjectColorFromScene(objectId);
        const messageOpacity = sceneColor?.opacity ?? 1.0;

        // Apply scene opacity multiplicatively
        renderable.userData.settings.opacity = newSceneOpacity * messageOpacity;
        log.info(`Message opacity: ${messageOpacity}, New scene opacity: ${newSceneOpacity}, Renderable opacity: ${renderable.userData.settings.opacity}`);

        // Trigger visual update to apply new opacity
        renderable.update(renderable.userData.collisionObject);
      }
    } else if (fieldName === "showCollisionObjects") {
      // Visibility will be recalculated in the next frame by startFrame()
      // No need to manually update all objects here
    } else if (fieldName === "topic") {
      // Topic changed - clear current scene and refetch from new topic
      const newTopic = value as string;
      log.info(`Planning scene topic changed to: ${newTopic}`);

      // Clear current scene data
      this.currentScene = undefined;
      this.initialSceneFetched = false;
      this.fetchingInitialScene = false;

      // Clear all renderables since we're switching topics
      this.removeAllRenderables();

      // Update the internal topic tracking
      this.topic = newTopic;

      // The subscription will be updated automatically by the renderer
      // when the settings tree is updated
    }
  };
}
