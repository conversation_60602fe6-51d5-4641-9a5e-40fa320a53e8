{"name": "@lichtblick/den", "description": "Incubation area for code that may be released under future @lichtblick packages", "license": "MPL-2.0", "private": true, "repository": {"type": "git", "url": "https://github.com/lichtblick-suite/lichtblick.git"}, "author": {"name": "Lichtblick", "email": "<EMAIL>"}, "homepage": "https://github.com/lichtblick-suite", "dependencies": {"@lichtblick/comlink": "1.0.3", "async-mutex": "0.4.0", "eventemitter3": "5.0.1", "xacro-parser": "0.3.9"}, "devDependencies": {"@lichtblick/log": "workspace:*", "@lichtblick/suite": "workspace:*", "@lichtblick/tsconfig": "1.0.0", "@types/dom-webcodecs": "0.1.14"}}