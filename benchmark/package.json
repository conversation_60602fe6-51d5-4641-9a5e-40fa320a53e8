{"name": "benchmark", "private": true, "dependencies": {"@lichtblick/suite-base": "workspace:*"}, "devDependencies": {"@foxglove/schemas": "1.6.6", "@lichtblick/den": "workspace:*", "@lichtblick/log": "workspace:*", "@lichtblick/rostime": "1.0.0", "@lichtblick/suite": "workspace:*", "@lichtblick/tsconfig": "1.0.0", "@pmmmwh/react-refresh-webpack-plugin": "0.5.11", "@types/react": "18.3.12", "@types/react-dom": "18.3.0", "clean-webpack-plugin": "4.0.0", "html-webpack-plugin": "5.6.0", "mathjs": "11.12.0", "react": "18.3.1", "react-dom": "18.2.0", "webpack": "5.99.9", "webpack-dev-server": "5.2.2"}}